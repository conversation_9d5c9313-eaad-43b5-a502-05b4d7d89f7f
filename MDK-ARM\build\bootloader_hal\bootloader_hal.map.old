Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    can.o(i.HAL_CAN_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    can.o(i.HAL_CAN_MspDeInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    can.o(i.HAL_CAN_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    can.o(i.HAL_CAN_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    can.o(i.HAL_CAN_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    can.o(i.MX_CAN_Init) refers to stm32f1xx_hal_can.o(i.HAL_CAN_Init) for HAL_CAN_Init
    can.o(i.MX_CAN_Init) refers to main.o(i.Error_Handler) for Error_Handler
    can.o(i.MX_CAN_Init) refers to can.o(.bss) for .bss
    canopen.o(i.CAN_User_Init) refers to stm32f1xx_hal_can.o(i.HAL_CAN_Init) for HAL_CAN_Init
    canopen.o(i.CAN_User_Init) refers to main.o(i.Error_Handler) for Error_Handler
    canopen.o(i.CAN_User_Init) refers to stm32f1xx_hal_can.o(i.HAL_CAN_ConfigFilter) for HAL_CAN_ConfigFilter
    canopen.o(i.CAN_User_Init) refers to stm32f1xx_hal_can.o(i.HAL_CAN_Start) for HAL_CAN_Start
    canopen.o(i.CAN_User_Init) refers to stm32f1xx_hal_can.o(i.HAL_CAN_ActivateNotification) for HAL_CAN_ActivateNotification
    canopen.o(i.CAN_User_Init) refers to canopen.o(.bss) for .bss
    canopen.o(i.CAN_User_Init) refers to canopen.o(.data) for .data
    canopen.o(i.HAL_CAN_RxFifo0MsgPendingCallback) refers to stm32f1xx_hal_can.o(i.HAL_CAN_GetRxMessage) for HAL_CAN_GetRxMessage
    canopen.o(i.HAL_CAN_RxFifo0MsgPendingCallback) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    canopen.o(i.HAL_CAN_RxFifo0MsgPendingCallback) refers to printf8.o(i.__0printf$8) for __2printf
    canopen.o(i.HAL_CAN_RxFifo0MsgPendingCallback) refers to canopen.o(.data) for .data
    canopen.o(i.HAL_CAN_RxFifo0MsgPendingCallback) refers to canopen.o(.bss) for .bss
    canopen.o(i.check_receive_timeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    canopen.o(i.check_receive_timeout) refers to canopen.o(i.firmware_stream_init) for firmware_stream_init
    canopen.o(i.check_receive_timeout) refers to canopen.o(.data) for .data
    canopen.o(i.firmware_stream_init) refers to canopen.o(.bss) for .bss
    canopen.o(i.firmware_stream_init) refers to canopen.o(.data) for .data
    canopen.o(i.reset_firmware_state) refers to canopen.o(i.firmware_stream_init) for firmware_stream_init
    canopen.o(.data) refers to canopen.o(.bss) for CAN_RX_BUF_A
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    iap.o(i.iap_erase_app_area) refers to printf8.o(i.__0printf$8) for __2printf
    iap.o(i.iap_erase_app_area) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock) for HAL_FLASH_Unlock
    iap.o(i.iap_erase_app_area) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) for HAL_FLASHEx_Erase
    iap.o(i.iap_erase_app_area) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock) for HAL_FLASH_Lock
    iap.o(i.iap_load_app) refers to sys.o(.emb_text) for MSR_MSP
    iap.o(i.iap_load_app) refers to iap.o(.data) for .data
    iap.o(i.iap_write_appbin) refers to stmflash.o(i.STMFLASH_Write) for STMFLASH_Write
    iap.o(i.iap_write_appbin) refers to iap.o(.bss) for .bss
    iap.o(i.iap_write_block) refers to printf8.o(i.__0printf$8) for __2printf
    iap.o(i.iap_write_block) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock) for HAL_FLASH_Unlock
    iap.o(i.iap_write_block) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) for HAL_FLASH_Program
    iap.o(i.iap_write_block) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock) for HAL_FLASH_Lock
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to can.o(i.MX_CAN_Init) for MX_CAN_Init
    main.o(i.main) refers to canopen.o(i.CAN_User_Init) for CAN_User_Init
    main.o(i.main) refers to canopen.o(i.firmware_stream_init) for firmware_stream_init
    main.o(i.main) refers to printf8.o(i.__0printf$8) for __2printf
    main.o(i.main) refers to iap.o(i.iap_load_app) for iap_load_app
    main.o(i.main) refers to iap.o(i.iap_erase_app_area) for iap_erase_app_area
    main.o(i.main) refers to canopen.o(i.reset_firmware_state) for reset_firmware_state
    main.o(i.main) refers to iap.o(i.iap_write_block) for iap_write_block
    main.o(i.main) refers to canopen.o(i.check_receive_timeout) for check_receive_timeout
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.main) refers to can.o(.bss) for hcan
    main.o(i.main) refers to canopen.o(.data) for jump_to_app_cmd
    main.o(i.main) refers to main.o(.data) for .data
    main.o(i.main) refers to main.o(.conststring) for .conststring
    stm32f1xx_it.o(i.SysTick_Handler) refers to stm32f1xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f1xx_it.o(i.USART2_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f1xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32f1xx_it.o(i.USB_LP_CAN1_RX0_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) for HAL_CAN_IRQHandler
    stm32f1xx_it.o(i.USB_LP_CAN1_RX0_IRQHandler) refers to can.o(.bss) for hcan
    stmflash.o(i.STMFLASH_Write) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock) for HAL_FLASH_Unlock
    stmflash.o(i.STMFLASH_Write) refers to stmflash.o(i.STMFLASH_Read) for STMFLASH_Read
    stmflash.o(i.STMFLASH_Write) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) for HAL_FLASHEx_Erase
    stmflash.o(i.STMFLASH_Write) refers to stmflash.o(i.STMFLASH_Write_NoCheck) for STMFLASH_Write_NoCheck
    stmflash.o(i.STMFLASH_Write) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock) for HAL_FLASH_Lock
    stmflash.o(i.STMFLASH_Write) refers to stmflash.o(.bss) for .bss
    stmflash.o(i.STMFLASH_Write_NoCheck) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) for HAL_FLASH_Program
    stmflash.o(i.Test_Write) refers to stmflash.o(i.STMFLASH_Write) for STMFLASH_Write
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.data) for .data
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.constdata) for .constdata
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.MX_USART2_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.fputc) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    usart.o(i.fputc) refers to usart.o(.bss) for .bss
    stm32f1xx_hal.o(i.HAL_DeInit) refers to stm32f1xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickPrio) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_IncTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_InitTick) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal_can.o(i.HAL_CAN_DeInit) refers to stm32f1xx_hal_can.o(i.HAL_CAN_Stop) for HAL_CAN_Stop
    stm32f1xx_hal_can.o(i.HAL_CAN_DeInit) refers to can.o(i.HAL_CAN_MspDeInit) for HAL_CAN_MspDeInit
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox0CompleteCallback) for HAL_CAN_TxMailbox0CompleteCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox0AbortCallback) for HAL_CAN_TxMailbox0AbortCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox1CompleteCallback) for HAL_CAN_TxMailbox1CompleteCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox1AbortCallback) for HAL_CAN_TxMailbox1AbortCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox2CompleteCallback) for HAL_CAN_TxMailbox2CompleteCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox2AbortCallback) for HAL_CAN_TxMailbox2AbortCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo0FullCallback) for HAL_CAN_RxFifo0FullCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to canopen.o(i.HAL_CAN_RxFifo0MsgPendingCallback) for HAL_CAN_RxFifo0MsgPendingCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo1FullCallback) for HAL_CAN_RxFifo1FullCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo1MsgPendingCallback) for HAL_CAN_RxFifo1MsgPendingCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_SleepCallback) for HAL_CAN_SleepCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_WakeUpFromRxMsgCallback) for HAL_CAN_WakeUpFromRxMsgCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_ErrorCallback) for HAL_CAN_ErrorCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_Init) refers to can.o(i.HAL_CAN_MspInit) for HAL_CAN_MspInit
    stm32f1xx_hal_can.o(i.HAL_CAN_Init) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_can.o(i.HAL_CAN_Start) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_can.o(i.HAL_CAN_Stop) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset) for HAL_NVIC_SystemReset
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to llushr.o(.text) for __aeabi_llsr
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe) for PWR_OverloadWfe
    stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.constdata) for AHBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to stm32f1xx_hal_rcc.o(.constdata) for .constdata
    stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc_ex.o(.constdata) for .constdata
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(STACK) for __initial_sp
    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(.text) for Reset_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.USB_LP_CAN1_RX0_IRQHandler) for USB_LP_CAN1_RX0_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f103xb.o(.text) refers to system_stm32f1xx.o(i.SystemInit) for SystemInit
    startup_stm32f103xb.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to usart.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to usart.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to usart.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to usart.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to usart.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to usart.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to usart.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to usart.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to usart.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to usart.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to usart.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to usart.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to usart.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to usart.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to usart.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to usart.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to usart.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to usart.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to usart.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to usart.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to usart.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to usart.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f103xb.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f103xb.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing can.o(.rev16_text), (4 bytes).
    Removing can.o(.revsh_text), (4 bytes).
    Removing can.o(.rrx_text), (6 bytes).
    Removing can.o(i.HAL_CAN_MspDeInit), (56 bytes).
    Removing canopen.o(.rev16_text), (4 bytes).
    Removing canopen.o(.revsh_text), (4 bytes).
    Removing canopen.o(.rrx_text), (6 bytes).
    Removing canopen.o(.bss), (24 bytes).
    Removing canopen.o(.data), (1 bytes).
    Removing canopen.o(.data), (8 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing iap.o(.rev16_text), (4 bytes).
    Removing iap.o(.revsh_text), (4 bytes).
    Removing iap.o(.rrx_text), (6 bytes).
    Removing iap.o(i.iap_write_appbin), (96 bytes).
    Removing iap.o(.bss), (1024 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(.data), (1 bytes).
    Removing stm32f1xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_it.o(.rrx_text), (6 bytes).
    Removing stmflash.o(.rev16_text), (4 bytes).
    Removing stmflash.o(.revsh_text), (4 bytes).
    Removing stmflash.o(.rrx_text), (6 bytes).
    Removing stmflash.o(i.STMFLASH_Read), (26 bytes).
    Removing stmflash.o(i.STMFLASH_ReadHalfWord), (4 bytes).
    Removing stmflash.o(i.STMFLASH_Write), (216 bytes).
    Removing stmflash.o(i.STMFLASH_Write_NoCheck), (44 bytes).
    Removing stmflash.o(i.Test_Write), (12 bytes).
    Removing stmflash.o(.bss), (1024 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.rrx_text), (6 bytes).
    Removing sys.o(i.INTX_DISABLE), (4 bytes).
    Removing sys.o(i.INTX_ENABLE), (4 bytes).
    Removing sys.o(i.WFI_SET), (4 bytes).
    Removing system_stm32f1xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f1xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f1xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f1xx.o(i.SystemCoreClockUpdate), (104 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (52 bytes).
    Removing usart.o(i._sys_exit), (2 bytes).
    Removing stm32f1xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DeInit), (32 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f1xx_hal_can.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_can.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_can.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_AbortTxRequest), (70 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_AddTxMessage), (188 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_DeInit), (40 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_DeactivateNotification), (36 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_GetError), (4 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_GetRxFifoFillLevel), (34 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_GetState), (36 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_GetTxMailboxesFreeLevel), (44 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_GetTxTimestamp), (40 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_IsSleepActive), (28 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_IsTxMessagePending), (30 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_MspInit), (2 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_RequestSleep), (38 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_ResetError), (34 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo0MsgPendingCallback), (2 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_Stop), (96 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_WakeUp), (76 bytes).
    Removing stm32f1xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.DMA_SetConfig), (42 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit), (92 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler), (340 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Init), (92 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (532 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (74 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start), (80 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT), (112 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (82 bytes).
    Removing stm32f1xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (104 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (140 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (164 bytes).
    Removing stm32f1xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (264 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (4 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (36 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT), (80 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (100 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (72 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase), (84 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetUserData), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (200 bytes).
    Removing stm32f1xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit), (280 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_ConfigEventout), (20 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_DisableEventout), (16 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_EnableEventout), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (68 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe), (6 bytes).
    Removing stm32f1xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit), (220 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (144 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (72 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (44 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (164 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (236 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.constdata), (18 bytes).
    Removing stm32f1xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (240 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (74 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop), (112 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive), (176 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT), (28 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (120 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMAError), (74 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt), (134 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt), (30 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_EndTxTransfer), (28 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA), (144 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT), (54 bytes).
    Removing startup_stm32f103xb.o(HEAP), (512 bytes).
    Removing dadd.o(.text), (334 bytes).
    Removing dmul.o(.text), (228 bytes).
    Removing ddiv.o(.text), (222 bytes).
    Removing dfixul.o(.text), (48 bytes).
    Removing cdrcmple.o(.text), (48 bytes).
    Removing depilogue.o(.text), (186 bytes).

278 unused section(s) (total 14822 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  useno.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    .\..\Core\Src\can.c                      0x00000000   Number         0  can.o ABSOLUTE
    .\..\Core\Src\canopen.c                  0x00000000   Number         0  canopen.o ABSOLUTE
    .\..\Core\Src\gpio.c                     0x00000000   Number         0  gpio.o ABSOLUTE
    .\..\Core\Src\iap.c                      0x00000000   Number         0  iap.o ABSOLUTE
    .\..\Core\Src\main.c                     0x00000000   Number         0  main.o ABSOLUTE
    .\..\Core\Src\stm32f1xx_hal_msp.c        0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    .\..\Core\Src\stm32f1xx_it.c             0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    .\..\Core\Src\stmflash.c                 0x00000000   Number         0  stmflash.o ABSOLUTE
    .\..\Core\Src\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    .\..\Core\Src\system_stm32f1xx.c         0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    .\..\Core\Src\usart.c                    0x00000000   Number         0  usart.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_can.c 0x00000000   Number         0  stm32f1xx_hal_can.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    .\..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    .\\..\\Core\\Src\\can.c                  0x00000000   Number         0  can.o ABSOLUTE
    .\\..\\Core\\Src\\canopen.c              0x00000000   Number         0  canopen.o ABSOLUTE
    .\\..\\Core\\Src\\gpio.c                 0x00000000   Number         0  gpio.o ABSOLUTE
    .\\..\\Core\\Src\\iap.c                  0x00000000   Number         0  iap.o ABSOLUTE
    .\\..\\Core\\Src\\main.c                 0x00000000   Number         0  main.o ABSOLUTE
    .\\..\\Core\\Src\\stm32f1xx_hal_msp.c    0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    .\\..\\Core\\Src\\stm32f1xx_it.c         0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    .\\..\\Core\\Src\\stmflash.c             0x00000000   Number         0  stmflash.o ABSOLUTE
    .\\..\\Core\\Src\\sys.c                  0x00000000   Number         0  sys.o ABSOLUTE
    .\\..\\Core\\Src\\system_stm32f1xx.c     0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    .\\..\\Core\\Src\\usart.c                0x00000000   Number         0  usart.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_can.c 0x00000000   Number         0  stm32f1xx_hal_can.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    .\\..\\Drivers\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    .\startup_stm32f103xb.s                  0x00000000   Number         0  startup_stm32f103xb.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f103xb.o(RESET)
    .ARM.Collect$$$$00000000                 0x080000ec   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080000ec   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080000f0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080000f4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080000f4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080000f4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x080000fc   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x080000fc   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x080000fc   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080000fc   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .emb_text                                0x08000100   Section        6  sys.o(.emb_text)
    .text                                    0x08000108   Section       36  startup_stm32f103xb.o(.text)
    .text                                    0x0800012c   Section        0  llushr.o(.text)
    .text                                    0x0800014c   Section        0  memseta.o(.text)
    .text                                    0x08000170   Section        0  uldiv.o(.text)
    .text                                    0x080001d4   Section       36  init.o(.text)
    .text                                    0x080001f8   Section        0  llshl.o(.text)
    i.BusFault_Handler                       0x08000216   Section        0  stm32f1xx_it.o(i.BusFault_Handler)
    i.CAN_User_Init                          0x08000218   Section        0  canopen.o(i.CAN_User_Init)
    i.DebugMon_Handler                       0x08000298   Section        0  stm32f1xx_it.o(i.DebugMon_Handler)
    i.Error_Handler                          0x0800029a   Section        0  main.o(i.Error_Handler)
    i.FLASH_MassErase                        0x080002a0   Section        0  stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase)
    FLASH_MassErase                          0x080002a1   Thumb Code    26  stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase)
    i.FLASH_PageErase                        0x080002c4   Section        0  stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase)
    i.FLASH_Program_HalfWord                 0x080002e8   Section        0  stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord)
    FLASH_Program_HalfWord                   0x080002e9   Thumb Code    20  stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord)
    i.FLASH_SetErrorCode                     0x08000304   Section        0  stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode)
    FLASH_SetErrorCode                       0x08000305   Thumb Code    84  stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode)
    i.FLASH_WaitForLastOperation             0x08000360   Section        0  stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation)
    i.HAL_CAN_ActivateNotification           0x080003b4   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_ActivateNotification)
    i.HAL_CAN_ConfigFilter                   0x080003d8   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_ConfigFilter)
    i.HAL_CAN_ErrorCallback                  0x080004c6   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_ErrorCallback)
    i.HAL_CAN_GetRxMessage                   0x080004c8   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_GetRxMessage)
    i.HAL_CAN_IRQHandler                     0x080005bc   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler)
    i.HAL_CAN_Init                           0x080007b8   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_Init)
    i.HAL_CAN_MspInit                        0x080008d4   Section        0  can.o(i.HAL_CAN_MspInit)
    i.HAL_CAN_RxFifo0FullCallback            0x08000950   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo0FullCallback)
    i.HAL_CAN_RxFifo0MsgPendingCallback      0x08000954   Section        0  canopen.o(i.HAL_CAN_RxFifo0MsgPendingCallback)
    i.HAL_CAN_RxFifo1FullCallback            0x08000a88   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo1FullCallback)
    i.HAL_CAN_RxFifo1MsgPendingCallback      0x08000a8a   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo1MsgPendingCallback)
    i.HAL_CAN_SleepCallback                  0x08000a8c   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_SleepCallback)
    i.HAL_CAN_Start                          0x08000a90   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_Start)
    i.HAL_CAN_TxMailbox0AbortCallback        0x08000ae8   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox0AbortCallback)
    i.HAL_CAN_TxMailbox0CompleteCallback     0x08000aea   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox0CompleteCallback)
    i.HAL_CAN_TxMailbox1AbortCallback        0x08000aec   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox1AbortCallback)
    i.HAL_CAN_TxMailbox1CompleteCallback     0x08000aee   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox1CompleteCallback)
    i.HAL_CAN_TxMailbox2AbortCallback        0x08000af0   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox2AbortCallback)
    i.HAL_CAN_TxMailbox2CompleteCallback     0x08000af2   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox2CompleteCallback)
    i.HAL_CAN_WakeUpFromRxMsgCallback        0x08000af4   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_WakeUpFromRxMsgCallback)
    i.HAL_DMA_Abort                          0x08000af6   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08000b3c   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_Delay                              0x08000bd4   Section        0  stm32f1xx_hal.o(i.HAL_Delay)
    i.HAL_FLASHEx_Erase                      0x08000bf8   Section        0  stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase)
    i.HAL_FLASH_Lock                         0x08000ca0   Section        0  stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock)
    i.HAL_FLASH_Program                      0x08000cb4   Section        0  stm32f1xx_hal_flash.o(i.HAL_FLASH_Program)
    i.HAL_FLASH_Unlock                       0x08000d34   Section        0  stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock)
    i.HAL_GPIO_Init                          0x08000d5c   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_WritePin                      0x08000f3c   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08000f48   Section        0  stm32f1xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08000f54   Section        0  stm32f1xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08000f64   Section        0  stm32f1xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08000f88   Section        0  stm32f1xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08000fc8   Section        0  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08000ff4   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08001010   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08001050   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08001074   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x080011a0   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x080011c0   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x080011e0   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x0800122c   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x0800154c   Section        0  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_UARTEx_RxEventCallback             0x08001574   Section        0  stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_ErrorCallback                 0x08001576   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x08001578   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x080017e4   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08001848   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_RxCpltCallback                0x080018c8   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_Transmit                      0x080018ca   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x0800196a   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x0800196c   Section        0  stm32f1xx_it.o(i.HardFault_Handler)
    i.MX_CAN_Init                            0x08001970   Section        0  can.o(i.MX_CAN_Init)
    i.MX_GPIO_Init                           0x080019b4   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_USART2_UART_Init                    0x08001a24   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MemManage_Handler                      0x08001a5c   Section        0  stm32f1xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08001a5e   Section        0  stm32f1xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x08001a60   Section        0  stm32f1xx_it.o(i.PendSV_Handler)
    i.SVC_Handler                            0x08001a62   Section        0  stm32f1xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08001a64   Section        0  stm32f1xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08001a68   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08001ac6   Section        0  system_stm32f1xx.o(i.SystemInit)
    i.UART_DMAAbortOnError                   0x08001ac8   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08001ac9   Thumb Code    16  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_EndRxTransfer                     0x08001ad8   Section        0  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08001ad9   Thumb Code    78  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_Receive_IT                        0x08001b26   Section        0  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08001b27   Thumb Code   194  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08001be8   Section        0  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08001be9   Thumb Code   178  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    i.UART_WaitOnFlagUntilTimeout            0x08001ca0   Section        0  stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x08001ca1   Thumb Code   114  stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART2_IRQHandler                      0x08001d14   Section        0  stm32f1xx_it.o(i.USART2_IRQHandler)
    i.USB_LP_CAN1_RX0_IRQHandler             0x08001d20   Section        0  stm32f1xx_it.o(i.USB_LP_CAN1_RX0_IRQHandler)
    i.UsageFault_Handler                     0x08001d2c   Section        0  stm32f1xx_it.o(i.UsageFault_Handler)
    i.__0printf$8                            0x08001d30   Section        0  printf8.o(i.__0printf$8)
    i.__NVIC_SetPriority                     0x08001d50   Section        0  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08001d51   Thumb Code    32  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__scatterload_copy                     0x08001d70   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08001d7e   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08001d80   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._printf_core                           0x08001d90   Section        0  printf8.o(i._printf_core)
    _printf_core                             0x08001d91   Thumb Code   996  printf8.o(i._printf_core)
    i._printf_post_padding                   0x080021a0   Section        0  printf8.o(i._printf_post_padding)
    _printf_post_padding                     0x080021a1   Thumb Code    36  printf8.o(i._printf_post_padding)
    i._printf_pre_padding                    0x080021c4   Section        0  printf8.o(i._printf_pre_padding)
    _printf_pre_padding                      0x080021c5   Thumb Code    46  printf8.o(i._printf_pre_padding)
    i.check_receive_timeout                  0x080021f4   Section        0  canopen.o(i.check_receive_timeout)
    i.firmware_stream_init                   0x08002240   Section        0  canopen.o(i.firmware_stream_init)
    i.fputc                                  0x08002268   Section        0  usart.o(i.fputc)
    i.iap_erase_app_area                     0x08002280   Section        0  iap.o(i.iap_erase_app_area)
    i.iap_load_app                           0x08002360   Section        0  iap.o(i.iap_load_app)
    i.iap_write_block                        0x08002394   Section        0  iap.o(i.iap_write_block)
    i.main                                   0x08002474   Section        0  main.o(i.main)
    i.reset_firmware_state                   0x08002974   Section        0  canopen.o(i.reset_firmware_state)
    .constdata                               0x08002978   Section       16  system_stm32f1xx.o(.constdata)
    .constdata                               0x08002988   Section        8  system_stm32f1xx.o(.constdata)
    .constdata                               0x08002990   Section       18  stm32f1xx_hal_rcc.o(.constdata)
    aPredivFactorTable                       0x08002990   Data           2  stm32f1xx_hal_rcc.o(.constdata)
    aPLLMULFactorTable                       0x08002992   Data          16  stm32f1xx_hal_rcc.o(.constdata)
    .conststring                             0x080029a4   Section      304  main.o(.conststring)
    .data                                    0x20000000   Section       40  canopen.o(.data)
    .data                                    0x20000028   Section        4  iap.o(.data)
    .data                                    0x2000002c   Section        4  main.o(.data)
    .data                                    0x20000030   Section        4  system_stm32f1xx.o(.data)
    .data                                    0x20000034   Section        4  usart.o(.data)
    .data                                    0x20000038   Section       12  stm32f1xx_hal.o(.data)
    .bss                                     0x20000044   Section       40  can.o(.bss)
    .bss                                     0x2000006c   Section     2100  canopen.o(.bss)
    .bss                                     0x200008a0   Section       72  usart.o(.bss)
    .bss                                     0x200008e8   Section       32  stm32f1xx_hal_flash.o(.bss)
    STACK                                    0x20000908   Section     1024  startup_stm32f103xb.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __use_no_errno                           0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_exception_handling              0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_fp                              0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_heap                            0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_heap_region                     0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_semihosting                     0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_semihosting_swi                 0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_signal_handling                 0x00000000   Number         0  useno.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f103xb.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f103xb.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f103xb.o(RESET)
    __main                                   0x080000ed   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080000ed   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080000f1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080000f5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080000f5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080000f5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080000f5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x080000fd   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x080000fd   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    MSR_MSP                                  0x08000101   Thumb Code     6  sys.o(.emb_text)
    Reset_Handler                            0x08000109   Thumb Code     8  startup_stm32f103xb.o(.text)
    ADC1_2_IRQHandler                        0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_RX1_IRQHandler                      0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_SCE_IRQHandler                      0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel1_IRQHandler                 0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel2_IRQHandler                 0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel3_IRQHandler                 0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel4_IRQHandler                 0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel5_IRQHandler                 0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel6_IRQHandler                 0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel7_IRQHandler                 0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI0_IRQHandler                         0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI15_10_IRQHandler                     0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI1_IRQHandler                         0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI2_IRQHandler                         0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI3_IRQHandler                         0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI4_IRQHandler                         0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI9_5_IRQHandler                       0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    FLASH_IRQHandler                         0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_ER_IRQHandler                       0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_EV_IRQHandler                       0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_ER_IRQHandler                       0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_EV_IRQHandler                       0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    PVD_IRQHandler                           0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    RCC_IRQHandler                           0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_Alarm_IRQHandler                     0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_IRQHandler                           0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI1_IRQHandler                          0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI2_IRQHandler                          0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    TAMPER_IRQHandler                        0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_BRK_IRQHandler                      0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_CC_IRQHandler                       0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_UP_IRQHandler                       0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM2_IRQHandler                          0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM3_IRQHandler                          0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM4_IRQHandler                          0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART1_IRQHandler                        0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART3_IRQHandler                        0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    USBWakeUp_IRQHandler                     0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    WWDG_IRQHandler                          0x08000123   Thumb Code     0  startup_stm32f103xb.o(.text)
    __aeabi_llsr                             0x0800012d   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x0800012d   Thumb Code     0  llushr.o(.text)
    __aeabi_memset                           0x0800014d   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x0800014d   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x0800014d   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x0800015b   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x0800015b   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x0800015b   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x0800015f   Thumb Code    18  memseta.o(.text)
    __aeabi_uldivmod                         0x08000171   Thumb Code    98  uldiv.o(.text)
    __scatterload                            0x080001d5   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x080001d5   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x080001f9   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080001f9   Thumb Code     0  llshl.o(.text)
    BusFault_Handler                         0x08000217   Thumb Code     2  stm32f1xx_it.o(i.BusFault_Handler)
    CAN_User_Init                            0x08000219   Thumb Code   120  canopen.o(i.CAN_User_Init)
    DebugMon_Handler                         0x08000299   Thumb Code     2  stm32f1xx_it.o(i.DebugMon_Handler)
    Error_Handler                            0x0800029b   Thumb Code     4  main.o(i.Error_Handler)
    FLASH_PageErase                          0x080002c5   Thumb Code    28  stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase)
    FLASH_WaitForLastOperation               0x08000361   Thumb Code    80  stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation)
    HAL_CAN_ActivateNotification             0x080003b5   Thumb Code    36  stm32f1xx_hal_can.o(i.HAL_CAN_ActivateNotification)
    HAL_CAN_ConfigFilter                     0x080003d9   Thumb Code   238  stm32f1xx_hal_can.o(i.HAL_CAN_ConfigFilter)
    HAL_CAN_ErrorCallback                    0x080004c7   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_ErrorCallback)
    HAL_CAN_GetRxMessage                     0x080004c9   Thumb Code   244  stm32f1xx_hal_can.o(i.HAL_CAN_GetRxMessage)
    HAL_CAN_IRQHandler                       0x080005bd   Thumb Code   508  stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler)
    HAL_CAN_Init                             0x080007b9   Thumb Code   282  stm32f1xx_hal_can.o(i.HAL_CAN_Init)
    HAL_CAN_MspInit                          0x080008d5   Thumb Code   112  can.o(i.HAL_CAN_MspInit)
    HAL_CAN_RxFifo0FullCallback              0x08000951   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo0FullCallback)
    HAL_CAN_RxFifo0MsgPendingCallback        0x08000955   Thumb Code   192  canopen.o(i.HAL_CAN_RxFifo0MsgPendingCallback)
    HAL_CAN_RxFifo1FullCallback              0x08000a89   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo1FullCallback)
    HAL_CAN_RxFifo1MsgPendingCallback        0x08000a8b   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo1MsgPendingCallback)
    HAL_CAN_SleepCallback                    0x08000a8d   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_SleepCallback)
    HAL_CAN_Start                            0x08000a91   Thumb Code    88  stm32f1xx_hal_can.o(i.HAL_CAN_Start)
    HAL_CAN_TxMailbox0AbortCallback          0x08000ae9   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox0AbortCallback)
    HAL_CAN_TxMailbox0CompleteCallback       0x08000aeb   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox0CompleteCallback)
    HAL_CAN_TxMailbox1AbortCallback          0x08000aed   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox1AbortCallback)
    HAL_CAN_TxMailbox1CompleteCallback       0x08000aef   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox1CompleteCallback)
    HAL_CAN_TxMailbox2AbortCallback          0x08000af1   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox2AbortCallback)
    HAL_CAN_TxMailbox2CompleteCallback       0x08000af3   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox2CompleteCallback)
    HAL_CAN_WakeUpFromRxMsgCallback          0x08000af5   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_WakeUpFromRxMsgCallback)
    HAL_DMA_Abort                            0x08000af7   Thumb Code    70  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08000b3d   Thumb Code   148  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_Delay                                0x08000bd5   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Delay)
    HAL_FLASHEx_Erase                        0x08000bf9   Thumb Code   160  stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase)
    HAL_FLASH_Lock                           0x08000ca1   Thumb Code    14  stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock)
    HAL_FLASH_Program                        0x08000cb5   Thumb Code   120  stm32f1xx_hal_flash.o(i.HAL_FLASH_Program)
    HAL_FLASH_Unlock                         0x08000d35   Thumb Code    28  stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock)
    HAL_GPIO_Init                            0x08000d5d   Thumb Code   446  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_WritePin                        0x08000f3d   Thumb Code    10  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08000f49   Thumb Code     6  stm32f1xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08000f55   Thumb Code    12  stm32f1xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08000f65   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08000f89   Thumb Code    54  stm32f1xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08000fc9   Thumb Code    38  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08000ff5   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08001011   Thumb Code    60  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08001051   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08001075   Thumb Code   280  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x080011a1   Thumb Code    20  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x080011c1   Thumb Code    20  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x080011e1   Thumb Code    58  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x0800122d   Thumb Code   778  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x0800154d   Thumb Code    40  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_UARTEx_RxEventCallback               0x08001575   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_ErrorCallback                   0x08001577   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08001579   Thumb Code   616  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x080017e5   Thumb Code   100  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08001849   Thumb Code   114  usart.o(i.HAL_UART_MspInit)
    HAL_UART_RxCpltCallback                  0x080018c9   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_Transmit                        0x080018cb   Thumb Code   160  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x0800196b   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x0800196d   Thumb Code     2  stm32f1xx_it.o(i.HardFault_Handler)
    MX_CAN_Init                              0x08001971   Thumb Code    60  can.o(i.MX_CAN_Init)
    MX_GPIO_Init                             0x080019b5   Thumb Code   104  gpio.o(i.MX_GPIO_Init)
    MX_USART2_UART_Init                      0x08001a25   Thumb Code    48  usart.o(i.MX_USART2_UART_Init)
    MemManage_Handler                        0x08001a5d   Thumb Code     2  stm32f1xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08001a5f   Thumb Code     2  stm32f1xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x08001a61   Thumb Code     2  stm32f1xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x08001a63   Thumb Code     2  stm32f1xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08001a65   Thumb Code     4  stm32f1xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08001a69   Thumb Code    94  main.o(i.SystemClock_Config)
    SystemInit                               0x08001ac7   Thumb Code     2  system_stm32f1xx.o(i.SystemInit)
    USART2_IRQHandler                        0x08001d15   Thumb Code     6  stm32f1xx_it.o(i.USART2_IRQHandler)
    USB_LP_CAN1_RX0_IRQHandler               0x08001d21   Thumb Code     6  stm32f1xx_it.o(i.USB_LP_CAN1_RX0_IRQHandler)
    UsageFault_Handler                       0x08001d2d   Thumb Code     2  stm32f1xx_it.o(i.UsageFault_Handler)
    __0printf$8                              0x08001d31   Thumb Code    22  printf8.o(i.__0printf$8)
    __1printf$8                              0x08001d31   Thumb Code     0  printf8.o(i.__0printf$8)
    __2printf                                0x08001d31   Thumb Code     0  printf8.o(i.__0printf$8)
    __scatterload_copy                       0x08001d71   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08001d7f   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08001d81   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    check_receive_timeout                    0x080021f5   Thumb Code    70  canopen.o(i.check_receive_timeout)
    firmware_stream_init                     0x08002241   Thumb Code    30  canopen.o(i.firmware_stream_init)
    fputc                                    0x08002269   Thumb Code    20  usart.o(i.fputc)
    iap_erase_app_area                       0x08002281   Thumb Code    66  iap.o(i.iap_erase_app_area)
    iap_load_app                             0x08002361   Thumb Code    42  iap.o(i.iap_load_app)
    iap_write_block                          0x08002395   Thumb Code    96  iap.o(i.iap_write_block)
    main                                     0x08002475   Thumb Code   486  main.o(i.main)
    reset_firmware_state                     0x08002975   Thumb Code     4  canopen.o(i.reset_firmware_state)
    AHBPrescTable                            0x08002978   Data          16  system_stm32f1xx.o(.constdata)
    APBPrescTable                            0x08002988   Data           8  system_stm32f1xx.o(.constdata)
    Region$$Table$$Base                      0x08002ad4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08002af4   Number         0  anon$$obj.o(Region$$Table)
    flash_write_pending                      0x20000000   Data           1  canopen.o(.data)
    jump_to_app_cmd                          0x20000001   Data           1  canopen.o(.data)
    erase_flash_cmd                          0x20000002   Data           1  canopen.o(.data)
    CAN_Baudrate                             0x20000004   Data           2  canopen.o(.data)
    current_rx_buffer                        0x20000008   Data           4  canopen.o(.data)
    current_flash_buffer                     0x2000000c   Data           4  canopen.o(.data)
    current_flash_addr                       0x20000010   Data           4  canopen.o(.data)
    total_received_bytes                     0x20000014   Data           4  canopen.o(.data)
    last_receive_time                        0x20000018   Data           4  canopen.o(.data)
    CAN1_RX_CNT                              0x2000001c   Data           4  canopen.o(.data)
    CAN_Rx_Data                              0x20000020   Data           8  canopen.o(.data)
    jumpapp                                  0x20000028   Data           4  iap.o(.data)
    bit_10s                                  0x2000002c   Data           1  main.o(.data)
    t                                        0x2000002d   Data           1  main.o(.data)
    clearflag                                0x2000002e   Data           1  main.o(.data)
    firmware_update_completed                0x2000002f   Data           1  main.o(.data)
    SystemCoreClock                          0x20000030   Data           4  system_stm32f1xx.o(.data)
    __stdout                                 0x20000034   Data           4  usart.o(.data)
    uwTickFreq                               0x20000038   Data           1  stm32f1xx_hal.o(.data)
    uwTickPrio                               0x2000003c   Data           4  stm32f1xx_hal.o(.data)
    uwTick                                   0x20000040   Data           4  stm32f1xx_hal.o(.data)
    hcan                                     0x20000044   Data          40  can.o(.bss)
    TxMeg                                    0x2000006c   Data          24  canopen.o(.bss)
    RxMeg                                    0x20000084   Data          28  canopen.o(.bss)
    CAN_RX_BUF_A                             0x200000a0   Data        1024  canopen.o(.bss)
    CAN_RX_BUF_B                             0x200004a0   Data        1024  canopen.o(.bss)
    huart2                                   0x200008a0   Data          72  usart.o(.bss)
    pFlash                                   0x200008e8   Data          32  stm32f1xx_hal_flash.o(.bss)
    __initial_sp                             0x20000d08   Data           0  startup_stm32f103xb.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00002b38, Max: 0x00004fff, ABSOLUTE)

    Execution Region ER_IROM1 (Base: 0x08000000, Size: 0x00002af4, Max: 0x00004fff, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x000000ec   Data   RO         2279    RESET               startup_stm32f103xb.o
    0x080000ec   0x00000000   Code   RO         2284  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080000ec   0x00000004   Code   RO         2552    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080000f0   0x00000004   Code   RO         2555    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080000f4   0x00000000   Code   RO         2557    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080000f4   0x00000000   Code   RO         2559    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080000f4   0x00000008   Code   RO         2560    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080000fc   0x00000000   Code   RO         2562    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x080000fc   0x00000000   Code   RO         2564    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x080000fc   0x00000004   Code   RO         2553    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000100   0x00000006   Code   RO          497    .emb_text           sys.o
    0x08000106   0x00000002   PAD
    0x08000108   0x00000024   Code   RO         2280    .text               startup_stm32f103xb.o
    0x0800012c   0x00000020   Code   RO         2287    .text               mc_w.l(llushr.o)
    0x0800014c   0x00000024   Code   RO         2289    .text               mc_w.l(memseta.o)
    0x08000170   0x00000062   Code   RO         2568    .text               mc_w.l(uldiv.o)
    0x080001d2   0x00000002   PAD
    0x080001d4   0x00000024   Code   RO         2581    .text               mc_w.l(init.o)
    0x080001f8   0x0000001e   Code   RO         2583    .text               mc_w.l(llshl.o)
    0x08000216   0x00000002   Code   RO          360    i.BusFault_Handler  stm32f1xx_it.o
    0x08000218   0x00000080   Code   RO          133    i.CAN_User_Init     canopen.o
    0x08000298   0x00000002   Code   RO          361    i.DebugMon_Handler  stm32f1xx_it.o
    0x0800029a   0x00000004   Code   RO          291    i.Error_Handler     main.o
    0x0800029e   0x00000002   PAD
    0x080002a0   0x00000024   Code   RO         1429    i.FLASH_MassErase   stm32f1xx_hal_flash_ex.o
    0x080002c4   0x00000024   Code   RO         1434    i.FLASH_PageErase   stm32f1xx_hal_flash_ex.o
    0x080002e8   0x0000001c   Code   RO         1326    i.FLASH_Program_HalfWord  stm32f1xx_hal_flash.o
    0x08000304   0x0000005c   Code   RO         1327    i.FLASH_SetErrorCode  stm32f1xx_hal_flash.o
    0x08000360   0x00000054   Code   RO         1328    i.FLASH_WaitForLastOperation  stm32f1xx_hal_flash.o
    0x080003b4   0x00000024   Code   RO          809    i.HAL_CAN_ActivateNotification  stm32f1xx_hal_can.o
    0x080003d8   0x000000ee   Code   RO          811    i.HAL_CAN_ConfigFilter  stm32f1xx_hal_can.o
    0x080004c6   0x00000002   Code   RO          814    i.HAL_CAN_ErrorCallback  stm32f1xx_hal_can.o
    0x080004c8   0x000000f4   Code   RO          817    i.HAL_CAN_GetRxMessage  stm32f1xx_hal_can.o
    0x080005bc   0x000001fc   Code   RO          821    i.HAL_CAN_IRQHandler  stm32f1xx_hal_can.o
    0x080007b8   0x0000011a   Code   RO          822    i.HAL_CAN_Init      stm32f1xx_hal_can.o
    0x080008d2   0x00000002   PAD
    0x080008d4   0x0000007c   Code   RO            5    i.HAL_CAN_MspInit   can.o
    0x08000950   0x00000002   Code   RO          829    i.HAL_CAN_RxFifo0FullCallback  stm32f1xx_hal_can.o
    0x08000952   0x00000002   PAD
    0x08000954   0x00000134   Code   RO          134    i.HAL_CAN_RxFifo0MsgPendingCallback  canopen.o
    0x08000a88   0x00000002   Code   RO          831    i.HAL_CAN_RxFifo1FullCallback  stm32f1xx_hal_can.o
    0x08000a8a   0x00000002   Code   RO          832    i.HAL_CAN_RxFifo1MsgPendingCallback  stm32f1xx_hal_can.o
    0x08000a8c   0x00000002   Code   RO          833    i.HAL_CAN_SleepCallback  stm32f1xx_hal_can.o
    0x08000a8e   0x00000002   PAD
    0x08000a90   0x00000058   Code   RO          834    i.HAL_CAN_Start     stm32f1xx_hal_can.o
    0x08000ae8   0x00000002   Code   RO          836    i.HAL_CAN_TxMailbox0AbortCallback  stm32f1xx_hal_can.o
    0x08000aea   0x00000002   Code   RO          837    i.HAL_CAN_TxMailbox0CompleteCallback  stm32f1xx_hal_can.o
    0x08000aec   0x00000002   Code   RO          838    i.HAL_CAN_TxMailbox1AbortCallback  stm32f1xx_hal_can.o
    0x08000aee   0x00000002   Code   RO          839    i.HAL_CAN_TxMailbox1CompleteCallback  stm32f1xx_hal_can.o
    0x08000af0   0x00000002   Code   RO          840    i.HAL_CAN_TxMailbox2AbortCallback  stm32f1xx_hal_can.o
    0x08000af2   0x00000002   Code   RO          841    i.HAL_CAN_TxMailbox2CompleteCallback  stm32f1xx_hal_can.o
    0x08000af4   0x00000002   Code   RO          843    i.HAL_CAN_WakeUpFromRxMsgCallback  stm32f1xx_hal_can.o
    0x08000af6   0x00000046   Code   RO         1160    i.HAL_DMA_Abort     stm32f1xx_hal_dma.o
    0x08000b3c   0x00000098   Code   RO         1161    i.HAL_DMA_Abort_IT  stm32f1xx_hal_dma.o
    0x08000bd4   0x00000024   Code   RO          641    i.HAL_Delay         stm32f1xx_hal.o
    0x08000bf8   0x000000a8   Code   RO         1435    i.HAL_FLASHEx_Erase  stm32f1xx_hal_flash_ex.o
    0x08000ca0   0x00000014   Code   RO         1332    i.HAL_FLASH_Lock    stm32f1xx_hal_flash.o
    0x08000cb4   0x00000080   Code   RO         1337    i.HAL_FLASH_Program  stm32f1xx_hal_flash.o
    0x08000d34   0x00000028   Code   RO         1339    i.HAL_FLASH_Unlock  stm32f1xx_hal_flash.o
    0x08000d5c   0x000001e0   Code   RO         1517    i.HAL_GPIO_Init     stm32f1xx_hal_gpio.o
    0x08000f3c   0x0000000a   Code   RO         1521    i.HAL_GPIO_WritePin  stm32f1xx_hal_gpio.o
    0x08000f46   0x00000002   PAD
    0x08000f48   0x0000000c   Code   RO          645    i.HAL_GetTick       stm32f1xx_hal.o
    0x08000f54   0x00000010   Code   RO          651    i.HAL_IncTick       stm32f1xx_hal.o
    0x08000f64   0x00000024   Code   RO          652    i.HAL_Init          stm32f1xx_hal.o
    0x08000f88   0x00000040   Code   RO          653    i.HAL_InitTick      stm32f1xx_hal.o
    0x08000fc8   0x0000002c   Code   RO          336    i.HAL_MspInit       stm32f1xx_hal_msp.o
    0x08000ff4   0x0000001a   Code   RO         1044    i.HAL_NVIC_EnableIRQ  stm32f1xx_hal_cortex.o
    0x0800100e   0x00000002   PAD
    0x08001010   0x00000040   Code   RO         1050    i.HAL_NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08001050   0x00000024   Code   RO         1051    i.HAL_NVIC_SetPriorityGrouping  stm32f1xx_hal_cortex.o
    0x08001074   0x0000012c   Code   RO         1742    i.HAL_RCC_ClockConfig  stm32f1xx_hal_rcc.o
    0x080011a0   0x00000020   Code   RO         1749    i.HAL_RCC_GetPCLK1Freq  stm32f1xx_hal_rcc.o
    0x080011c0   0x00000020   Code   RO         1750    i.HAL_RCC_GetPCLK2Freq  stm32f1xx_hal_rcc.o
    0x080011e0   0x0000004c   Code   RO         1751    i.HAL_RCC_GetSysClockFreq  stm32f1xx_hal_rcc.o
    0x0800122c   0x00000320   Code   RO         1754    i.HAL_RCC_OscConfig  stm32f1xx_hal_rcc.o
    0x0800154c   0x00000028   Code   RO         1055    i.HAL_SYSTICK_Config  stm32f1xx_hal_cortex.o
    0x08001574   0x00000002   Code   RO         1929    i.HAL_UARTEx_RxEventCallback  stm32f1xx_hal_uart.o
    0x08001576   0x00000002   Code   RO         1943    i.HAL_UART_ErrorCallback  stm32f1xx_hal_uart.o
    0x08001578   0x0000026c   Code   RO         1946    i.HAL_UART_IRQHandler  stm32f1xx_hal_uart.o
    0x080017e4   0x00000064   Code   RO         1947    i.HAL_UART_Init     stm32f1xx_hal_uart.o
    0x08001848   0x00000080   Code   RO          576    i.HAL_UART_MspInit  usart.o
    0x080018c8   0x00000002   Code   RO         1953    i.HAL_UART_RxCpltCallback  stm32f1xx_hal_uart.o
    0x080018ca   0x000000a0   Code   RO         1955    i.HAL_UART_Transmit  stm32f1xx_hal_uart.o
    0x0800196a   0x00000002   Code   RO         1958    i.HAL_UART_TxCpltCallback  stm32f1xx_hal_uart.o
    0x0800196c   0x00000002   Code   RO          362    i.HardFault_Handler  stm32f1xx_it.o
    0x0800196e   0x00000002   PAD
    0x08001970   0x00000044   Code   RO            6    i.MX_CAN_Init       can.o
    0x080019b4   0x00000070   Code   RO          208    i.MX_GPIO_Init      gpio.o
    0x08001a24   0x00000038   Code   RO          577    i.MX_USART2_UART_Init  usart.o
    0x08001a5c   0x00000002   Code   RO          363    i.MemManage_Handler  stm32f1xx_it.o
    0x08001a5e   0x00000002   Code   RO          364    i.NMI_Handler       stm32f1xx_it.o
    0x08001a60   0x00000002   Code   RO          365    i.PendSV_Handler    stm32f1xx_it.o
    0x08001a62   0x00000002   Code   RO          366    i.SVC_Handler       stm32f1xx_it.o
    0x08001a64   0x00000004   Code   RO          367    i.SysTick_Handler   stm32f1xx_it.o
    0x08001a68   0x0000005e   Code   RO          292    i.SystemClock_Config  main.o
    0x08001ac6   0x00000002   Code   RO          538    i.SystemInit        system_stm32f1xx.o
    0x08001ac8   0x00000010   Code   RO         1960    i.UART_DMAAbortOnError  stm32f1xx_hal_uart.o
    0x08001ad8   0x0000004e   Code   RO         1970    i.UART_EndRxTransfer  stm32f1xx_hal_uart.o
    0x08001b26   0x000000c2   Code   RO         1972    i.UART_Receive_IT   stm32f1xx_hal_uart.o
    0x08001be8   0x000000b8   Code   RO         1973    i.UART_SetConfig    stm32f1xx_hal_uart.o
    0x08001ca0   0x00000072   Code   RO         1976    i.UART_WaitOnFlagUntilTimeout  stm32f1xx_hal_uart.o
    0x08001d12   0x00000002   PAD
    0x08001d14   0x0000000c   Code   RO          368    i.USART2_IRQHandler  stm32f1xx_it.o
    0x08001d20   0x0000000c   Code   RO          369    i.USB_LP_CAN1_RX0_IRQHandler  stm32f1xx_it.o
    0x08001d2c   0x00000002   Code   RO          370    i.UsageFault_Handler  stm32f1xx_it.o
    0x08001d2e   0x00000002   PAD
    0x08001d30   0x00000020   Code   RO         2498    i.__0printf$8       mc_w.l(printf8.o)
    0x08001d50   0x00000020   Code   RO         1057    i.__NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08001d70   0x0000000e   Code   RO         2591    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08001d7e   0x00000002   Code   RO         2592    i.__scatterload_null  mc_w.l(handlers.o)
    0x08001d80   0x0000000e   Code   RO         2593    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08001d8e   0x00000002   PAD
    0x08001d90   0x00000410   Code   RO         2505    i._printf_core      mc_w.l(printf8.o)
    0x080021a0   0x00000024   Code   RO         2506    i._printf_post_padding  mc_w.l(printf8.o)
    0x080021c4   0x0000002e   Code   RO         2507    i._printf_pre_padding  mc_w.l(printf8.o)
    0x080021f2   0x00000002   PAD
    0x080021f4   0x0000004c   Code   RO          135    i.check_receive_timeout  canopen.o
    0x08002240   0x00000028   Code   RO          136    i.firmware_stream_init  canopen.o
    0x08002268   0x00000018   Code   RO          579    i.fputc             usart.o
    0x08002280   0x000000e0   Code   RO          235    i.iap_erase_app_area  iap.o
    0x08002360   0x00000034   Code   RO          236    i.iap_load_app      iap.o
    0x08002394   0x000000e0   Code   RO          238    i.iap_write_block   iap.o
    0x08002474   0x00000500   Code   RO          293    i.main              main.o
    0x08002974   0x00000004   Code   RO          137    i.reset_firmware_state  canopen.o
    0x08002978   0x00000010   Data   RO          539    .constdata          system_stm32f1xx.o
    0x08002988   0x00000008   Data   RO          540    .constdata          system_stm32f1xx.o
    0x08002990   0x00000012   Data   RO         1755    .constdata          stm32f1xx_hal_rcc.o
    0x080029a2   0x00000002   PAD
    0x080029a4   0x00000130   Data   RO          294    .conststring        main.o
    0x08002ad4   0x00000020   Data   RO         2589    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Base: 0x20000000, Size: 0x00000d08, Max: 0x00005000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x00000028   Data   RW          140    .data               canopen.o
    0x20000028   0x00000004   Data   RW          240    .data               iap.o
    0x2000002c   0x00000004   Data   RW          296    .data               main.o
    0x20000030   0x00000004   Data   RW          541    .data               system_stm32f1xx.o
    0x20000034   0x00000004   Data   RW          581    .data               usart.o
    0x20000038   0x0000000c   Data   RW          659    .data               stm32f1xx_hal.o
    0x20000044   0x00000028   Zero   RW            7    .bss                can.o
    0x2000006c   0x00000834   Zero   RW          139    .bss                canopen.o
    0x200008a0   0x00000048   Zero   RW          580    .bss                usart.o
    0x200008e8   0x00000020   Zero   RW         1340    .bss                stm32f1xx_hal_flash.o
    0x20000908   0x00000400   Zero   RW         2277    STACK               startup_stm32f103xb.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       192         20          0          0         40     412235   can.o
       556        148          0         40       2100       4646   canopen.o
       112          8          0          0          0        879   gpio.o
       500        296          0          4          0       6159   iap.o
      1378        794        304          4          0       2780   main.o
        36          8        236          0       1024        756   startup_stm32f103xb.o
       164         28          0         12          0       5773   stm32f1xx_hal.o
      1420          0          0          0          0      12312   stm32f1xx_hal_can.o
       198         14          0          0          0      28835   stm32f1xx_hal_cortex.o
       222          4          0          0          0       1723   stm32f1xx_hal_dma.o
       392         46          0          0         32       4598   stm32f1xx_hal_flash.o
       240         26          0          0          0       3181   stm32f1xx_hal_flash_ex.o
       490         34          0          0          0       2844   stm32f1xx_hal_gpio.o
        44          6          0          0          0        802   stm32f1xx_hal_msp.o
      1240         84         18          0          0       5016   stm32f1xx_hal_rcc.o
      1474         10          0          0          0       9884   stm32f1xx_hal_uart.o
        44         12          0          0          0       4579   stm32f1xx_it.o
         6          0          0          0          0        328   sys.o
         2          0         24          4          0       1071   system_stm32f1xx.o
       208         26          0          4         72       3406   usart.o

    ----------------------------------------------------------------------
      8938       <USER>        <GROUP>         68       3268     511807   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        20          0          2          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
        30          0          0          0          0         68   llshl.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0        108   memseta.o
      1154         54          0          0          0        352   printf8.o
        98          0          0          0          0         92   uldiv.o

    ----------------------------------------------------------------------
      1442         <USER>          <GROUP>          0          0        756   Library Totals
         6          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1436         70          0          0          0        756   mc_w.l

    ----------------------------------------------------------------------
      1442         <USER>          <GROUP>          0          0        756   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     10380       1634        616         68       3268     508035   Grand Totals
     10380       1634        616         68       3268     508035   ELF Image Totals
     10380       1634        616         68          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                10996 (  10.74kB)
    Total RW  Size (RW Data + ZI Data)              3336 (   3.26kB)
    Total ROM Size (Code + RO Data + RW Data)      11064 (  10.80kB)

==============================================================================

